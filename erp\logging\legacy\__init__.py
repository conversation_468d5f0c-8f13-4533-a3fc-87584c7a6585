"""
Legacy Components
Legacy logging components maintained for backward compatibility

WARNING: These components are deprecated and will be removed in a future version.
Please migrate to the new modular architecture using the facade interface.
"""

import warnings

# Issue deprecation warning when legacy components are imported
warnings.warn(
    "Legacy logging components are deprecated. "
    "Please use the new facade interface from erp.logging.core.facade",
    DeprecationWarning,
    stacklevel=2
)

# Legacy formatters
from .formatters import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    J<PERSON>NFormatter,
    ColoredFormatter,
)

# Legacy handlers
from .handlers import (
    R<PERSON>tingFileHandler,
    TimedRotatingFileHandler,
    DatabaseHandler,
)

# Legacy filters
from .filters import (
    <PERSON>Filter,
    ModuleFilter,
    PerformanceFilter,
    DuplicateFilter,
)

# Legacy manager
from .manager import LoggingManager

__all__ = [
    # Legacy formatters
    'ERPFormatter',
    'JSONFormatter',
    'ColoredFormatter',
    
    # Legacy handlers
    'RotatingFileHandler',
    'TimedRotatingFileHandler',
    'DatabaseHandler',
    
    # Legacy filters
    'LevelFilter',
    'ModuleFilter',
    'PerformanceFilter',
    'DuplicateFilter',
    
    # Legacy manager
    'LoggingManager',
]
