"""
ERP Logging System - Organized Modular Architecture
Comprehensive logging module with focused, single-responsibility components

The logging system is now organized into logical subdirectories:
- core/: Core system components and facade interface
- config/: Configuration system
- utils/: Utility functions and decorators
- adapters/: Adapter classes for different backends
- middleware/: Middleware system for custom processing
- monitoring/: Performance monitoring and metrics
- coordination/: Operation coordination and rate limiting
- legacy/: Legacy components (deprecated, for backward compatibility)
"""

# Main facade interface (recommended for most use cases)
from .core import (
    LoggingFacade,
    get_logging_facade,
    initialize_logging,
    get_logger,
    start_performance_monitoring,
    stop_performance_monitoring,
    record_request,
    operation_context,
    quiet_operation,
    get_system_status,
    shutdown_logging,
    test_logging_colors,
    log_performance,
    log_structured,
    LogContext,
    log_method_calls,
    log_database_operations,
    log_api_calls,
    log_security_events,
)

# Configuration system
from .config import (
    LoggingConfig,
    FormatterConfig,
    HandlerConfig,
    FilterConfig,
    LoggerConfig,
    MonitoringConfig,
    ConfigLoader,
    ConfigValidator,
)

# Core system components (for advanced usage)
from .core import (
    LoggingSystem,
    ComponentRegistry,
    LoggerManager,
    get_logging_system,
)

# Factory system (for custom component creation)
from .core import (
    ComponentFactoryRegistry,
    FormatterFactory,
    FilterFactory,
    HandlerFactory,
    LoggerFactory,
    get_factory_registry,
)

# Utility functions and decorators
from .utils import (
    log_performance,
    log_structured,
    LogContext,
    log_method_calls,
    log_database_operations,
    log_api_calls,
    log_security_events,
)

# Middleware system (for custom processing pipelines)
from .middleware import (
    LoggingPipeline,
    LoggingMiddleware,
    ContextMiddleware,
    PerformanceMiddleware,
    SecurityMiddleware,
    FilterMiddleware,
    get_global_pipeline,
    process_log_record,
)

# Monitoring and coordination (for advanced monitoring)
from .monitoring import (
    PerformanceMonitor,
    SystemMetrics,
    ApplicationMetrics,
    MetricsCollector,
    AlertManager,
    MetricsStorage,
)

from .coordination import (
    LoggingCoordinator,
    OperationTracker,
    LoggerSuppressor,
    LoggingRateLimiter,
    get_logging_coordinator,
)

# Adapters (for custom backends)
from .adapters import (
    LoggingAdapter,
    ConsoleAdapter,
    FileAdapter,
    DatabaseAdapter,
    RemoteAdapter,
)

# Legacy components (for backward compatibility)
from .legacy import (
    ERPFormatter,
    JSONFormatter,
    ColoredFormatter,
    RotatingFileHandler,
    TimedRotatingFileHandler,
    DatabaseHandler,
    LevelFilter,
    ModuleFilter,
    PerformanceFilter,
    DuplicateFilter,
)

__all__ = [
    # Main facade interface (recommended)
    'LoggingFacade',
    'get_logging_facade',
    'initialize_logging',
    'get_logger',
    'start_performance_monitoring',
    'stop_performance_monitoring',
    'record_request',
    'operation_context',
    'quiet_operation',
    'get_system_status',
    'shutdown_logging',
    'test_logging_colors',

    # Utility functions and decorators
    'log_performance',
    'log_structured',
    'LogContext',
    'log_method_calls',
    'log_database_operations',
    'log_api_calls',
    'log_security_events',

    # Configuration system
    'LoggingConfig',
    'FormatterConfig',
    'HandlerConfig',
    'FilterConfig',
    'LoggerConfig',
    'MonitoringConfig',
    'ConfigLoader',
    'ConfigValidator',

    # Core system (advanced usage)
    'LoggingSystem',
    'ComponentRegistry',
    'LoggerManager',
    'get_logging_system',

    # Factory system (custom components)
    'ComponentFactoryRegistry',
    'FormatterFactory',
    'FilterFactory',
    'HandlerFactory',
    'LoggerFactory',
    'get_factory_registry',

    # Middleware system (custom processing)
    'LoggingPipeline',
    'LoggingMiddleware',
    'ContextMiddleware',
    'PerformanceMiddleware',
    'SecurityMiddleware',
    'FilterMiddleware',
    'get_global_pipeline',
    'process_log_record',

    # Monitoring and coordination (advanced monitoring)
    'PerformanceMonitor',
    'SystemMetrics',
    'ApplicationMetrics',
    'MetricsCollector',
    'AlertManager',
    'MetricsStorage',
    'LoggingCoordinator',
    'OperationTracker',
    'LoggerSuppressor',
    'LoggingRateLimiter',
    'get_logging_coordinator',

    # Adapters (custom backends)
    'LoggingAdapter',
    'ConsoleAdapter',
    'FileAdapter',
    'DatabaseAdapter',
    'RemoteAdapter',

    # Legacy components (backward compatibility)
    'ERPFormatter',
    'JSONFormatter',
    'ColoredFormatter',
    'RotatingFileHandler',
    'TimedRotatingFileHandler',
    'DatabaseHandler',
    'LevelFilter',
    'ModuleFilter',
    'PerformanceFilter',
    'DuplicateFilter',
]
