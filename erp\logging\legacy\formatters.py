"""
Logging Formatters - Custom formatters for different output formats
"""
import logging
import json
import traceback
import os
from datetime import datetime
from typing import Dict, Any, Optional
import sys


class ERPFormatter(logging.Formatter):
    """Enhanced formatter for ERP system with predefined beautiful formatting"""

    def __init__(self, include_context=True, use_unicode=None):
        # Auto-detect Unicode support
        if use_unicode is None:
            try:
                # Test if we can encode Unicode characters
                test_str = "│"
                test_str.encode(sys.stdout.encoding or 'utf-8')
                self.use_unicode = True
            except (UnicodeEncodeError, AttributeError):
                self.use_unicode = False
        else:
            self.use_unicode = use_unicode

        # Choose separator based on Unicode support
        separator = " │ " if self.use_unicode else " | "

        # Predefined format with proper spacing and structure
        fmt = f"%(asctime)s{separator}%(levelname)-8s{separator}%(name)-25s{separator}%(message)s"
        datefmt = "%Y-%m-%d %H:%M:%S"
        super().__init__(fmt, datefmt)
        self.include_context = include_context

    def format(self, record):
        """Format log record with ERP-specific enhancements and beautiful spacing"""
        # Store original values
        original_levelname = record.levelname
        original_name = record.name

        try:
            # Add ERP-specific attributes with defaults
            if not hasattr(record, 'user_id'):
                record.user_id = 'system'
            if not hasattr(record, 'database'):
                record.database = 'default'
            if not hasattr(record, 'request_id'):
                record.request_id = None

            # Ensure consistent padding for level name (8 characters)
            record.levelname = f"{record.levelname:<8s}"

            # Truncate and pad module name for consistency (25 characters)
            module_name = record.name
            if len(module_name) > 25:
                module_name = "..." + module_name[-22:]
            record.name = f"{module_name:<25s}"

            # Format the base message
            formatted = super().format(record)

            # Add context information with beautiful formatting
            context_parts = []
            separator = " │ " if self.use_unicode else " | "

            # Add database info if not default
            if hasattr(record, 'database') and record.database != 'default':
                context_parts.append(f"DB:{record.database}")

            # Add user info if not system
            if hasattr(record, 'user_id') and record.user_id != 'system':
                context_parts.append(f"User:{record.user_id}")

            # Add request ID if present
            if hasattr(record, 'request_id') and record.request_id:
                req_id = record.request_id[:8] if len(record.request_id) > 8 else record.request_id
                context_parts.append(f"Req:{req_id}")

            # Add operation type if present
            if hasattr(record, 'operation'):
                context_parts.append(f"Op:{record.operation}")

            # Add duration if present (performance logging)
            if hasattr(record, 'duration'):
                duration_icon = "⏱" if self.use_unicode else "T:"
                try:
                    # Handle both float and string duration values
                    if isinstance(record.duration, (int, float)):
                        context_parts.append(f"{duration_icon}{record.duration:.3f}s")
                    else:
                        context_parts.append(f"{duration_icon}{record.duration}")
                except (ValueError, TypeError):
                    context_parts.append(f"{duration_icon}{record.duration}")

            # Add error type if present
            if hasattr(record, 'error_type'):
                context_parts.append(f"Error:{record.error_type}")

            # Add status code if present
            if hasattr(record, 'status_code'):
                context_parts.append(f"Status:{record.status_code}")

            # Add extra context if available
            if self.include_context and hasattr(record, 'extra_context'):
                for k, v in record.extra_context.items():
                    if k not in ['user_id', 'database', 'request_id', 'operation', 'duration', 'error_type', 'status_code']:
                        context_parts.append(f"{k}:{v}")

            # Append context with proper formatting
            if context_parts:
                formatted += f"{separator}{separator.join(context_parts)}"

            return formatted

        finally:
            # Restore original values
            record.levelname = original_levelname
            record.name = original_name


class JSONFormatter(logging.Formatter):
    """JSON formatter for structured logging"""
    
    def __init__(self, include_extra=True, indent=None):
        super().__init__()
        self.include_extra = include_extra
        self.indent = indent
        
    def format(self, record):
        """Format log record as JSON"""
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'thread': record.thread,
            'thread_name': record.threadName,
            'process': record.process,
        }
        
        # Add ERP-specific fields
        erp_fields = ['user_id', 'database', 'request_id', 'addon_name', 'model_name']
        for field in erp_fields:
            if hasattr(record, field):
                log_entry[field] = getattr(record, field)
                
        # Add exception information
        if record.exc_info:
            log_entry['exception'] = {
                'type': record.exc_info[0].__name__,
                'message': str(record.exc_info[1]),
                'traceback': traceback.format_exception(*record.exc_info)
            }
            
        # Add extra fields if enabled
        if self.include_extra:
            extra_fields = {}
            for key, value in record.__dict__.items():
                if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                              'filename', 'module', 'lineno', 'funcName', 'created',
                              'msecs', 'relativeCreated', 'thread', 'threadName',
                              'processName', 'process', 'getMessage', 'exc_info',
                              'exc_text', 'stack_info'] + erp_fields:
                    try:
                        # Only include JSON-serializable values
                        json.dumps(value)
                        extra_fields[key] = value
                    except (TypeError, ValueError):
                        extra_fields[key] = str(value)
                        
            if extra_fields:
                log_entry['extra'] = extra_fields
                
        return json.dumps(log_entry, indent=self.indent, default=str)


class ColoredFormatter(logging.Formatter):
    """Enhanced colored formatter with beautiful spacing and highlighting"""

    # Enhanced ANSI color codes with styles - improved colors for better visibility
    COLORS = {
        'DEBUG': '\033[96m',      # Bright Cyan - more visible than regular cyan
        'INFO': '\033[92m',       # Bright Green - more visible than regular green
        'WARNING': '\033[93m',    # Bright Yellow - more visible than regular yellow
        'ERROR': '\033[91m',      # Bright Red - more visible than regular red
        'CRITICAL': '\033[97;41m', # White on Red background - high contrast
        'RESET': '\033[0m',       # Reset
        'BOLD': '\033[1m',        # Bold
        'DIM': '\033[2m',         # Dim
        'BLUE': '\033[94m',       # Bright Blue
        'MAGENTA': '\033[95m',    # Bright Magenta
        'WHITE': '\033[97m',      # Bright White
        'GRAY': '\033[90m',       # Gray
        'GREEN': '\033[92m',      # Bright Green (for reuse)
    }

    def __init__(self, use_colors=None, use_unicode=None):
        # Auto-detect Unicode support
        if use_unicode is None:
            try:
                # Test if we can encode Unicode characters
                test_str = "│"
                test_str.encode(sys.stdout.encoding or 'utf-8')
                self.use_unicode = True
            except (UnicodeEncodeError, AttributeError):
                self.use_unicode = False
        else:
            self.use_unicode = use_unicode

        # Choose separator based on Unicode support
        separator = " │ " if self.use_unicode else " | "

        # Predefined beautiful format with proper spacing and consistent padding
        fmt = f"%(asctime)s{separator}%(levelname)-8s{separator}%(name)-25s{separator}%(message)s"
        datefmt = "%H:%M:%S"  # Shorter time format for console
        super().__init__(fmt, datefmt)

        # Auto-detect color support (improved Windows detection)
        if use_colors is None:
            self.use_colors = (
                hasattr(sys.stdout, 'isatty') and sys.stdout.isatty() and
                (sys.platform != 'win32' or 'ANSICON' in os.environ or 'WT_SESSION' in os.environ)
            )
        else:
            self.use_colors = use_colors

    def format(self, record):
        """Format log record with enhanced colors and highlighting"""
        if not self.use_colors:
            # Use ERPFormatter for non-colored output with consistent formatting
            erp_formatter = ERPFormatter(use_unicode=self.use_unicode)
            return erp_formatter.format(record)

        # Store original values
        original_levelname = record.levelname
        original_name = record.name
        original_message = record.getMessage()

        try:
            # Add ERP-specific attributes with defaults if missing
            if not hasattr(record, 'user_id'):
                record.user_id = 'system'
            if not hasattr(record, 'database'):
                record.database = 'default'
            if not hasattr(record, 'request_id'):
                record.request_id = None

            # Get color codes
            level_color = self.COLORS.get(record.levelname, '')
            reset = self.COLORS['RESET']
            bold = self.COLORS['BOLD']
            dim = self.COLORS['DIM']
            blue = self.COLORS['BLUE']
            gray = self.COLORS['GRAY']
            green = self.COLORS['GREEN']

            # Format level with color and consistent width (8 characters)
            if level_color:
                record.levelname = f"{level_color}{bold}{record.levelname:<8s}{reset}"
            else:
                record.levelname = f"{record.levelname:<8s}"

            # Color and truncate module name for readability (25 characters)
            module_name = record.name
            if len(module_name) > 25:
                module_name = "..." + module_name[-22:]
            record.name = f"{blue}{module_name:<25s}{reset}"

            # Format the base message first
            formatted = super().format(record)

            # Add context information with colors
            context_parts = []
            separator = f" {gray}│{reset} " if self.use_unicode else f" {gray}|{reset} "

            # Database info with color
            if hasattr(record, 'database') and record.database != 'default':
                context_parts.append(f"{blue}DB{reset}:{bold}{record.database}{reset}")

            # User info with color
            if hasattr(record, 'user_id') and record.user_id != 'system':
                context_parts.append(f"{blue}User{reset}:{bold}{record.user_id}{reset}")

            # Request ID with color
            if hasattr(record, 'request_id') and record.request_id:
                req_id = record.request_id[:8] if len(record.request_id) > 8 else record.request_id
                context_parts.append(f"{blue}Req{reset}:{dim}{req_id}{reset}")

            # Operation type with color
            if hasattr(record, 'operation'):
                context_parts.append(f"{blue}Op{reset}:{record.operation}")

            # Duration with special formatting
            if hasattr(record, 'duration'):
                duration = record.duration
                duration_icon = "⏱" if self.use_unicode else "T:"
                try:
                    if isinstance(duration, (int, float)):
                        duration_color = (self.COLORS['ERROR'] if duration > 2.0 else
                                        self.COLORS['WARNING'] if duration > 1.0 else
                                        green)
                        context_parts.append(f"{duration_icon}{duration_color}{duration:.3f}s{reset}")
                    else:
                        context_parts.append(f"{duration_icon}{duration}")
                except (ValueError, TypeError):
                    context_parts.append(f"{duration_icon}{duration}")

            # Error type highlighting
            if hasattr(record, 'error_type'):
                context_parts.append(f"{self.COLORS['ERROR']}Error{reset}:{bold}{record.error_type}{reset}")

            # Status code highlighting
            if hasattr(record, 'status_code'):
                status = record.status_code
                status_color = (self.COLORS['ERROR'] if status >= 500 else
                              self.COLORS['WARNING'] if status >= 400 else
                              green)
                context_parts.append(f"{status_color}{status}{reset}")

            # Append context with proper formatting
            if context_parts:
                formatted += f"{separator}{separator.join(context_parts)}"

            return formatted

        finally:
            # Restore original values
            record.levelname = original_levelname
            record.name = original_name


class PerformanceFormatter(logging.Formatter):
    """Enhanced formatter for performance-related logs with visual indicators"""

    def __init__(self, use_unicode=None):
        # Auto-detect Unicode support
        if use_unicode is None:
            try:
                test_str = "│⚡⏱"
                test_str.encode(sys.stdout.encoding or 'utf-8')
                self.use_unicode = True
            except (UnicodeEncodeError, AttributeError):
                self.use_unicode = False
        else:
            self.use_unicode = use_unicode

        # Choose format based on Unicode support
        if self.use_unicode:
            fmt = '%(asctime)s │ ⚡ PERF │ %(name)-20s │ %(message)s │ ⏱%(duration).3fs'
        else:
            fmt = '%(asctime)s | PERF | %(name)-20s | %(message)s | T:%(duration).3fs'

        datefmt = '%H:%M:%S'
        super().__init__(fmt, datefmt)

    def format(self, record):
        """Format performance log record with enhanced visual indicators"""
        # Ensure duration is present
        if not hasattr(record, 'duration'):
            record.duration = 0.0

        # Add performance-specific formatting with operation context
        message_parts = []

        if hasattr(record, 'operation'):
            message_parts.append(f"🔧 {record.operation}")

        message_parts.append(record.getMessage())

        # Add performance indicators based on duration
        duration = record.duration
        if self.use_unicode:
            if duration > 5.0:
                message_parts.append("🐌 VERY SLOW")
            elif duration > 2.0:
                message_parts.append("🐢 SLOW")
            elif duration > 1.0:
                message_parts.append("⚠️ MODERATE")
        else:
            if duration > 5.0:
                message_parts.append("VERY SLOW")
            elif duration > 2.0:
                message_parts.append("SLOW")
            elif duration > 1.0:
                message_parts.append("MODERATE")

        record.message = " │ ".join(message_parts)

        return super().format(record)


class DatabaseFormatter(logging.Formatter):
    """Enhanced formatter for database-related logs with query highlighting"""

    def __init__(self):
        # Predefined format optimized for database operations
        fmt = '%(asctime)s │ 🗄️  DB │ %(database)-12s │ %(message)s'
        datefmt = '%H:%M:%S'
        super().__init__(fmt, datefmt)

    def format(self, record):
        """Format database log record with enhanced query information"""
        # Ensure database field is present with proper formatting
        if not hasattr(record, 'database'):
            record.database = 'default'

        # Truncate database name if too long
        if len(record.database) > 12:
            record.database = record.database[:9] + "..."

        message_parts = [record.getMessage()]

        # Add SQL query information with smart truncation
        if hasattr(record, 'query'):
            query = record.query.strip()
            # Extract query type (SELECT, INSERT, UPDATE, DELETE)
            query_type = query.split()[0].upper() if query else 'QUERY'

            if len(query) > 80:
                query_preview = query[:77] + "..."
            else:
                query_preview = query

            message_parts.append(f"📝 {query_type}: {query_preview}")

        # Add query duration if present
        if hasattr(record, 'query_duration'):
            duration = record.query_duration
            duration_indicator = "🐌" if duration > 1.0 else "⚡" if duration < 0.1 else "⏱️"
            message_parts.append(f"{duration_indicator} {duration:.3f}s")

        # Add affected rows if present
        if hasattr(record, 'affected_rows'):
            message_parts.append(f"📊 {record.affected_rows} rows")

        record.message = " │ ".join(message_parts)

        return super().format(record)


class SecurityFormatter(logging.Formatter):
    """Enhanced formatter for security-related logs with threat indicators"""

    def __init__(self):
        # Predefined format optimized for security monitoring
        fmt = '%(asctime)s │ 🔒 SEC │ %(levelname)-8s │ %(message)s'
        datefmt = '%Y-%m-%d %H:%M:%S'  # Full timestamp for security logs
        super().__init__(fmt, datefmt)

    def format(self, record):
        """Format security log record with enhanced threat indicators"""
        # Ensure security fields are present
        if not hasattr(record, 'user_id'):
            record.user_id = 'anonymous'
        if not hasattr(record, 'client_ip'):
            record.client_ip = 'unknown'

        message_parts = []

        # Add security event type with appropriate icon
        if hasattr(record, 'security_event'):
            event_icons = {
                'login_success': '✅',
                'login_failure': '❌',
                'permission_denied': '🚫',
                'suspicious_activity': '⚠️',
                'data_access': '📋',
                'admin_action': '👑',
                'password_change': '🔑',
                'account_locked': '🔒',
                'failed_login': '❌',
                'brute_force': '💥'
            }
            icon = event_icons.get(record.security_event, '🔍')
            message_parts.append(f"{icon} {record.security_event.upper()}")

        message_parts.append(record.getMessage())

        # Add user information with formatting
        if record.user_id != 'anonymous':
            message_parts.append(f"👤 {record.user_id}")

        # Add IP address with geolocation hint if available
        if record.client_ip != 'unknown':
            ip_display = record.client_ip
            if hasattr(record, 'country'):
                ip_display += f" ({record.country})"
            message_parts.append(f"🌐 {ip_display}")

        # Add session information if present
        if hasattr(record, 'session_id'):
            session_short = record.session_id[:8] if len(record.session_id) > 8 else record.session_id
            message_parts.append(f"🎫 {session_short}")

        # Add risk level indicator
        if hasattr(record, 'risk_level'):
            risk_icons = {'low': '🟢', 'medium': '🟡', 'high': '🟠', 'critical': '🔴'}
            risk_icon = risk_icons.get(record.risk_level.lower(), '⚪')
            message_parts.append(f"{risk_icon} {record.risk_level.upper()}")

        record.message = " │ ".join(message_parts)

        return super().format(record)
